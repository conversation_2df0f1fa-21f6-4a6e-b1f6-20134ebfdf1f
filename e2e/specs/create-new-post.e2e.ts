import {
  getSharedTestContext,
  resetSharedTestContext,
  cleanupTestState,
  executeCommand,
  expectNotice,
  expectPostFile,
  type SharedTestContext
} from '../helpers/shared-context';
import { completeModalInteraction } from '../helpers/modal-helpers';
import { test, beforeAll, beforeEach, afterEach } from 'vitest';

describe("Commands: Create New Post", () => {
  let context: SharedTestContext;

  beforeAll(async () => { context = await getSharedTestContext(); });

  beforeEach(async () => { await resetSharedTestContext(); });

  afterEach(async () => { await cleanupTestState(); });

  test("should create a new post using Playwright Electron", async () => {
    const testTitle = "Test Post";

    await executeCommand(context, 'Ghost Sync: Create new post');

    // Use the new modal helpers for cleaner interaction
    await completeModalInteraction(
      context.page,
      'create-post',
      { 'post-title': testTitle },
      'submit'
    );

    await expectNotice(context, "Created new post");
    await expectPostFile(context, "test-post", { content: /Write your content here/ });
  });
});
