import {
  getSharedTestContext,
  resetSharedTestContext,
  cleanupTestState,
  executeCommand,
  waitForNotice,
  getPostFile,
  type SharedTestContext
} from '../helpers/shared-context';
import { completeModalInteraction } from '../helpers/modal-helpers';
import { test, beforeAll, beforeEach, afterEach, expect } from 'vitest';

describe("Commands: Create New Post", () => {
  let context: SharedTestContext;

  beforeAll(async () => { context = await getSharedTestContext(); });

  beforeEach(async () => { await resetSharedTestContext(); });

  afterEach(async () => { await cleanupTestState(); });

  test("should create a new post using Playwright Electron", async () => {
    const testTitle = "Test Post";

    await executeCommand(context, 'Ghost Sync: Create new post');

    // Use the new modal helpers for cleaner interaction
    await completeModalInteraction(
      context.page,
      'create-post',
      { 'post-title': testTitle },
      'submit'
    );

    const noticeAppeared = await waitForNotice(context, "Created new post");
    expect(noticeAppeared).toBe(true);

    const postFile = await getPostFile(context, "testpost");
    expect(postFile.exists).toBe(true);
    expect(postFile.content).toMatch(/Write your content here/);
  });
});
