import type { Page, ElectronApplication } from 'playwright';
import { resetObsidianUI } from './plugin-setup';
import * as fs from 'fs';
import * as path from 'path';
import { randomUUID } from 'crypto';

/**
 * Shared context for all e2e tests
 * Provides access to the global Electron instance
 */
export interface SharedTestContext {
  page: Page;
  electronApp: ElectronApplication;
  testId?: string;
  vaultPath?: string;
  dataPath?: string;
}

let sharedContext: SharedTestContext | null = null;

/**
 * Get the shared test context
 * This connects to the Electron instance launched in setup
 */
export async function getSharedTestContext(): Promise<SharedTestContext> {
  if (sharedContext) {
    return sharedContext;
  }

  // Get from global variables set by setup-shared-electron.ts
  const globalElectronApp = (global as any).__SHARED_ELECTRON_APP__;
  const globalPage = (global as any).__SHARED_ELECTRON_PAGE__;

  if (globalElectronApp && globalPage) {
    console.log("✅ Using shared Electron instance");
    sharedContext = {
      electronApp: globalElectronApp,
      page: globalPage
    };
    return sharedContext;
  }

  // If not available, try to get it from the setup module
  try {
    const { getSharedElectron } = await import('../setup-shared-electron');
    const { electronApp, page } = await getSharedElectron();

    sharedContext = {
      electronApp,
      page
    };

    return sharedContext;
  } catch (error) {
    throw new Error(
      `Shared Electron instance not available: ${error.message}. ` +
      'Make sure the setup completed successfully.'
    );
  }
}

/**
 * Reset UI state for the next test
 * This should be called in beforeEach hooks
 */
export async function resetSharedTestContext(): Promise<void> {
  const context = await getSharedTestContext();
  await resetObsidianUI(context.page);
}

/**
 * Clean up test-specific state
 * This should be called in afterEach hooks
 */
export async function cleanupTestState(): Promise<void> {
  const context = await getSharedTestContext();
  await resetObsidianUI(context.page);

  // Clear any test files from the articles directory
  const articlesDir = path.join(process.cwd(), 'tests/vault/Test/articles');
  if (fs.existsSync(articlesDir)) {
    const files = fs.readdirSync(articlesDir);
    for (const file of files) {
      if (file.includes('test-') && file.endsWith('.md')) {
        try {
          fs.unlinkSync(path.join(articlesDir, file));
        } catch (error) {
          console.log(`⚠️ Could not remove test file ${file}:`, error.message);
        }
      }
    }
  }
}

export async function executeCommand(context: SharedTestContext, command: string): Promise<void> {
  await context.page.keyboard.press('Meta+P');
  await context.page.waitForSelector('.prompt-input');
  await context.page.fill('.prompt-input', command);
  await context.page.keyboard.press('Enter');
}

/**
 * Wait for and verify that a notice with specific text appears
 */
export async function expectNotice(context: SharedTestContext, expectedText: string, timeout: number = 5000): Promise<void> {
  await context.page.waitForFunction(
    ({ text }) => {
      const noticeElements = document.querySelectorAll('.notice');
      const notices = Array.from(noticeElements).map(el => el.textContent || '');
      return notices.some(notice => notice.includes(text));
    },
    { text: expectedText },
    { timeout }
  );
}

/**
 * Check if a post file exists and optionally validate its properties
 */
export async function expectPostFile(
  context: SharedTestContext,
  filename: string,
  options?: { title?: string; content?: RegExp }
): Promise<void> {
  await context.page.evaluate(
    async ({ filename, options }) => {
      const app = (window as any).app;
      if (!app || !app.vault || !app.metadataCache) {
        throw new Error('Obsidian app, vault, or metadataCache not available');
      }

      // Look for the file in the articles directory
      const articlesPath = 'articles';
      const fullPath = `${articlesPath}/${filename}.md`;

      const file = app.vault.getAbstractFileByPath(fullPath);
      if (!file) {
        throw new Error(`File not found: ${fullPath}`);
      }

      // Use Obsidian's metadata cache to get parsed frontmatter
      const fileCache = app.metadataCache.getFileCache(file);
      const frontmatter = fileCache?.frontmatter || {};

      // Get file content
      const fullContent = await app.vault.read(file);

      // Extract body content (content without frontmatter)
      let bodyContent = fullContent;
      if (fileCache?.frontmatterPosition) {
        // If frontmatter exists, extract content after it
        const frontmatterEnd = fileCache.frontmatterPosition.end;
        bodyContent = fullContent.slice(frontmatterEnd.offset).trim();
      }

      // Validate title if specified
      if (options?.title && frontmatter.title !== options.title) {
        throw new Error(`Expected title "${options.title}" but found "${frontmatter.title}"`);
      }

      // Validate content if specified
      if (options?.content) {
        const regex = new RegExp(options.content.source, options.content.flags);
        if (!regex.test(bodyContent)) {
          throw new Error(`Content does not match pattern: ${options.content}`);
        }
      }

      return {
        exists: true,
        title: frontmatter.title,
        content: bodyContent,
        path: fullPath
      };
    },
    { filename, options }
  );

  // The function will throw if validation fails, so if we reach here, the file exists and matches criteria
}

/**
 * Create an isolated test environment with unique vault and data directories
 */
export async function createIsolatedTestEnvironment(): Promise<{ testId: string; vaultPath: string; dataPath: string }> {
  const testId = randomUUID();
  const baseTestDir = path.resolve('./e2e/test-environments');
  const testDir = path.join(baseTestDir, testId);
  const vaultPath = path.join(testDir, 'vault');
  const dataPath = path.join(testDir, 'data');

  // Ensure base test directory exists
  await fs.promises.mkdir(baseTestDir, { recursive: true });
  await fs.promises.mkdir(testDir, { recursive: true });

  // Copy pristine vault to isolated vault
  const pristineVaultPath = path.resolve('./tests/vault/Test.pristine');
  if (fs.existsSync(pristineVaultPath)) {
    await copyDirectory(pristineVaultPath, vaultPath);
  } else {
    throw new Error('Pristine vault not found at tests/vault/Test.pristine');
  }

  // Copy pristine data directory to isolated data directory
  const pristineDataPath = path.resolve('./e2e/obsidian-data.pristine');
  if (fs.existsSync(pristineDataPath)) {
    await copyDirectory(pristineDataPath, dataPath);
  } else {
    // Create minimal data directory if pristine doesn't exist
    await fs.promises.mkdir(dataPath, { recursive: true });
  }

  return { testId, vaultPath, dataPath };
}

/**
 * Clean up isolated test environment
 */
export async function cleanupIsolatedTestEnvironment(testId: string): Promise<void> {
  const baseTestDir = path.resolve('./e2e/test-environments');
  const testDir = path.join(baseTestDir, testId);

  if (fs.existsSync(testDir)) {
    try {
      await fs.promises.rm(testDir, { recursive: true, force: true });
    } catch (error) {
      console.log(`⚠️ Failed to cleanup test environment ${testId}:`, error.message);
    }
  }
}

/**
 * Recursively copy directory
 */
async function copyDirectory(src: string, dest: string): Promise<void> {
  await fs.promises.mkdir(dest, { recursive: true });

  const entries = await fs.promises.readdir(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      await copyDirectory(srcPath, destPath);
    } else {
      await fs.promises.copyFile(srcPath, destPath);
    }
  }
}
